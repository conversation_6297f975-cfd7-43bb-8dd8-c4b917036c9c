package main

import "fmt"

//func main() {
//	var arr1 [3]int           //定义一个数组，数组的长度为3，数组的元素类型为int
//	arr2 := [3]int{1, 2, 3}   //定义一个数组，数组的长度为3，数组的元素类型为int
//	arr3 := [...]int{1, 2, 3} //定义一个数组，数组的长度由编译器自行判断，数组的元素类型为int
//	fmt.Println(arr1[0])      //访问数组元素
//	arr2[1] = 100             //修改数组元素
//
//	//遍历数组
//	for index, value := range arr3 {
//		fmt.Printf("arr3[%v] = %v\n", i, v)
//	}
//
//	//数组是值类型，传递的是副本
//	modify(arr3)
//	fmt.Println(arr3) //[1 2 3] 原数组未改变
//}
//
//func modify(a [3]int) {
//	a[0] = 100
//}

//	func main() {
//		arr := [3]int{}
//		//因为int默认是8个字节，所有下一个数组的地址是上一个数组地址加上8
//		fmt.Printf("arr的地址是：%p\n", &arr)       //arr的地址是：   0x14000016090
//		fmt.Printf("arr[0]的地址是：%p\n", &arr[0]) //arr[0]的地址是：0x14000016090
//		fmt.Printf("arr[1]的地址是：%p\n", &arr[1]) //arr[1]的地址是：0x14000016098
//		fmt.Printf("arr[2]的地址是：%p\n", &arr[2]) //arr[2]的地址是：0x140000160a0
//
//		for _, value := range arr {
//			fmt.Printf("value的地址是：%p\n", &value) //value的地址是：0x140000160a8
//		}
//
// }
//
//func change(p *[3]int) {
//	p[0] = 100
//}
//func main() {
//	var arr [3]int        // 声明一个长度为3的int数组
//	arr = [3]int{1, 2, 3} // 初始化
//	fmt.Printf("arr = %v\n", arr)
//	change(&arr)
//	fmt.Printf("arr = %v\n", arr)
//
//}

//func main() {
//	intValue := 10
//	valueAddr := &intValue
//	value := *valueAddr
//
//}

// 生成5个随机数
//func main() {
//	ints := [5]int{}
//	rand.Seed(time.Now().UnixNano())
//	for i := 0; i < 5; i++ {
//		ints[i] = rand.Intn(100)
//		fmt.Println(ints[i])
//	}
//
//	for i := 4; i >= 0; i-- {
//		fmt.Println(ints[i])
//	}
//}

//func main() {
//	arr := [5]int{0, 1, 2, 3, 4}
//	s := arr[1:3]
//	s = append(s, 33, 44)
//	s[0] = 11
//	fmt.Println(s)
//	fmt.Println(arr)
//	s = append(s, 55, 66, 77, 88)
//	fmt.Println(s)
//	fmt.Println(arr)
//}

//func main() {
//	s1 := []int{1, 2, 3}
//	s2 := make([]int, len(s1))
//	copy(s2, s1)
//
//	s2[0] = 100
//
//	fmt.Println(s1)
//	fmt.Println(s2)
//
//}

//func main() {
//	s := "你好go世界"
//	runes := []rune(s)
//	runes[0] = '不'
//	fmt.Println(string(runes))
//}

//func fbn(n int) []uint64 {
//	nums := make([]uint64, n)
//	for i := range nums {
//		if i <= 1 {
//			nums[i] = 1
//		} else {
//			nums[i] = nums[i-1] + nums[i-2]
//		}
//	}
//	return nums
//}
//
//func main() {
//	nums := fbn(10)
//	fmt.Println(nums)
//}

// 冒泡排序函数，对一个整数切片进行升序排序
//func bubbleSort(arr []int) {
//	for i := 0; i < len(arr)-1; i++ {
//		flag := false
//		for j := 0; j < len(arr)-1-i; j++ {
//			if arr[j] > arr[j+1] {
//				arr[j], arr[j+1] = arr[j+1], arr[j]
//				flag = true
//			}
//		}
//		if !flag {
//			break
//		}
//	}
//}
//func main() {
//	arr := []int{5, 4, 3, 2, 1}
//	bubbleSort(arr)
//	fmt.Println(arr)
//}

//func main() {
//	// 初始化一个整型切片并赋值
//	arr := []int{6, 3, 8, 2, 9, 1}
//	// 调用快速排序函数对数组进行排序
//	quickSort(arr, 0, len(arr)-1)
//	// 打印排序后的数组
//	fmt.Println("排序后：", arr)
//}
//
//// 快速排序函数，使用递归实现
//// 参数说明：
////   arr: 需要排序的整型切片
////   left: 当前排序段的左边界索引
////   right: 当前排序段的右边界索引
//func quickSort(arr []int, left int, right int) {
//	if left < right {
//		// 获取基准元素的位置
//		pivot := partition(arr, left, right)
//		// 对基准左侧子数组进行快速排序
//		quickSort(arr, left, pivot-1)
//		// 对基准右侧子数组进行快速排序
//		quickSort(arr, pivot+1, right)
//	}
//}
//
//// 分区函数，用于找到基准元素的正确位置，并将小于基准的元素移到其左边，大于基准的元素移到其右边
//// 参数说明：
////   arr: 需要分区的整型切片
////   left: 当前分区段的左边界索引
////   right: 当前分区段的右边界索引
//// 返回值：基准元素最终所在的位置索引
//func partition(arr []int, left int, right int) int {
//	// 选择最左侧元素作为基准值
//	pivot := arr[left]
//	// 循环直到左右指针相遇
//	for left < right {
//		// 从右向左寻找比基准小的元素
//		for left < right && arr[right] >= pivot {
//			right--
//		}
//		// 将比基准小的元素移动到左侧空位
//		arr[left] = arr[right]
//		// 从左向右寻找比基准大的元素
//		for left < right && arr[left] <= pivot {
//			left++
//		}
//		// 将比基准大的元素移动到右侧空位
//		arr[right] = arr[left]
//	}
//	// 将基准值放到正确的位置
//	arr[left] = pivot
//	// 返回基准值的位置
//	return left
//}

// 二分查找函数，在有序整型切片中查找目标值的位置
func binarySearch(arr []int, target int) int {
	left, right := 0, len(arr)-1
	for left <= right {
		mid := (left + right) / 2
		if arr[mid] == target {
			return mid
		} else if arr[mid] < target {
			left = mid + 1
		} else {
			right = mid - 1
		}
	}
	return -1
}

func main() {
	arr := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
	target := 5
	index := binarySearch(arr, target)
	fmt.Println("目标元素的索引是：", index)
}
