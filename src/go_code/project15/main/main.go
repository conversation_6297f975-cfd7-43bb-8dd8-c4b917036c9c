package main

import "fmt"

//	func main() {
//		student := model.NewStudent("lai", 23)
//		name := student.GetName()
//		fmt.Printf(name)
//	}
//type Animal struct {
//	Name string
//}
//
//func (a *Animal) Speak() {
//	fmt.Println(a.Name, "is making a sound")
//}
//
//// Dog 继承 Animal
//type Dog struct {
//	Animal // 匿名字段，相当于“继承”
//	Breed  string
//}
//
//func (d *Dog) Bark() {
//	fmt.Println(d.Name, "says: DogWoof!")
//}
//
//func (d *Animal) Bark() {
//	fmt.Println(d.Name, "says: AnimalWoof!")
//}
//
//func main() {
//	dog := Dog{
//		Animal: Animal{Name: "Buddy"},
//		Breed:  "Labrador",
//	}
//
//	dog.Animal.Name = "zhangsan"
//
//	dog.Animal.Speak()
//	dog.Speak()
//	dog.Animal.Bark()
//	dog.Bark()
//}

type Speaker interface {
	Speak()
}

type Dog struct {
	Name string
}

func (d Dog) Speak() {
	fmt.Println(d.Name, "says: Woof!")
}

func MakeItSpeak(s Speaker) {
	s.Speak()
}

func main() {
	var d = Dog{}
	d.Name = "Buddy"
	MakeItSpeak(d)
}
