package main

import (
	"fmt"
	"sort"
)

//	func main() {
//		var arr [2][3]int
//		arr[0][0] = 42
//
//		fmt.Printf("&arr         = %p, type: %T\n", &arr, &arr)
//		fmt.Printf("&arr[0][0]   = %p, type: %T\n", &arr[0][0], &arr[0][0])
//
//		// 你可以做类型转换
//		//p := &arr[0][0]
//		//q := (*int)(&arr[0][0])         // 合法
//		//r := (*[6]int)(p)               // 非法，类型不兼容，除非使用 unsafe
//	}
//func main() {
//	var arr [2][3]int64
//	for row, rowValue := range arr {
//		for col, colValue := range rowValue {
//			fmt.Printf("&arr[%d][%d] = %p, value: %d\n", row, col, &colValue, colValue)
//		}
//	}
//}

//func main() {
//	age := make(map[string]int)
//	age["laiwh"] = 22
//	age["laiwh2"] = 23
//	age["laiwh3"] = 24
//	age["laiwh4"] = 25
//	age["laiwh5"] = 26
//	age["laiwh6"] = 27
//	age["laiwh7"] = 28
//
//	delete(age, "laiwh7")
//	fmt.Println(age["laiwh"])
//
//	value, ok := age["laiwh"]
//	if ok {
//		fmt.Println(value)
//	}
//
//	for keys, values := range age {
//		fmt.Println(keys, values)
//	}
//
//	age["laiwh8"] = 29
//	age["laiwh11"] = 30
//	age["laiwh12"] = 31
//	age["laiwh14"] = 32
//
//	for keys, values := range age {
//		fmt.Println(keys, values)
//	}
//}

//func main() {
//	var m sync.Map
//	m.Store("laiwh", 22)
//	m.Store("laiwh2", 23)
//	m.Store("laiwh3", 24)
//	m.Delete("laiwh3")
//	value, ok := m.Load("laiwh")
//	if ok {
//		fmt.Println(value)
//	}
//}

// map的crud
//func main() {
//	m := make(map[string]map[string]string)
//	m["laiwh"] = make(map[string]string)
//	m["laiwh"]["name"] = "laiwh"
//	m["laiwh"]["age"] = "22"
//	m["laiwh"]["sex"] = "male"
//
//	m["zhansan"] = make(map[string]string)
//	m["zhansan"]["name"] = "female"
//	m["zhansan"]["age"] = "23"
//	m["zhansan"]["sex"] = "female"
//
//	fmt.Println(len(m["laiwh"]))
//
//	for k, v := range m {
//		fmt.Println(k, v)
//		for k1, v1 := range v {
//			fmt.Println(k1, v1)
//		}
//	}
//
//}

func main() {
	// 创建一个 key 和 value 都是 int 类型的 map
	m := make(map[int]int)
	// 给 map 添加键值对
	m[1] = 1
	m[2] = 2
	m[3] = 3
	m[4] = 4
	m[5] = 5
	m[6] = 6
	m[7] = 7
	m[8] = 8
	m[9] = 9
	m[10] = 10

	// 创建一个用于存储 map 中 value 的切片，初始长度为 0，容量为 map 的长度
	ints := make([]int, 0, len(m))
	// 将 map 中的所有 value 存入切片中
	for _, v := range m {
		ints = append(ints, v)
	}

	// 对切片进行升序排序
	sort.Ints(ints)

	// 遍历排序后的切片，并通过 value 打印对应的 key 和 value（此时 key 等于 value）
	for _, value := range ints {
		fmt.Printf("%v: %d\n", value, m[value])
	}
}
