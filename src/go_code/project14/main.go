package main

import "fmt"

//type Person struct {
//	Name string
//	Age  int
//}

//func (p Person) SayHello() {
//	fmt.Println("Hello, my name is", p.Name)
//}
//func (p *Person) GrowUp() {
//	p.Age++
//}
//
//func main() {
//	p := Person{"laiwh", 21}
//	p1 := Person{Name: "laiwqh", Age: 24}
//	fmt.Println(p.Name, p.Age)
//	fmt.Println(p1.Name, p1.Age)
//	p.Say<PERSON>ello()
//	p.GrowUp()
//	fmt.Println(p.Name, p.Age)
//	p1.SayHello()
//	p1.GrowUp()
//	fmt.Println(p1.Name, p1.Age)
//}

//type Example struct {
//	A int8  // 1 byte
//	B int32 // 4 bytes
//	C int16 // 2 bytes
//}
//
//type BadAlign struct {
//	A int8
//	B int64
//	C int8
//}
//
//// 占用 24 字节（有大量 padding）
//
//type GoodAlign struct {
//	B int64
//	A int8
//	C int8
//}

// 占用 16 字节（最小 padding）

//func main() {
//	var ba BadAlign
//	fmt.Println("Sizeof struct:", unsafe.Sizeof(ba))
//	fmt.Println("Offset of A:", unsafe.Offsetof(ba.A))
//	fmt.Println("Offset of B:", unsafe.Offsetof(ba.B))
//	fmt.Println("Offset of C:", unsafe.Offsetof(ba.C))
//	var ga GoodAlign
//	fmt.Println("Sizeof struct:", unsafe.Sizeof(ga))
//	fmt.Println("Offset of B:", unsafe.Offsetof(ga.B))
//	fmt.Println("Offset of A:", unsafe.Offsetof(ga.A))
//	fmt.Println("Offset of C:", unsafe.Offsetof(ga.C))
//}

//type Person struct {
//	Name string
//	Age  int
//}

//func main() {
//	var p1 Person
//	p1.Name = "laiwh"
//	p1.Age = 21
//
//	p2 := &p1
//	fmt.Println(p2.Name, p2.Age)
//	fmt.Println((*p2).Name, (*p2).Age)
//
//	p2.Name = "laiwh2"
//
//	fmt.Printf("p2.Name=%v p1.Name=%v\n", p2.Name, p1.Name)
//	fmt.Printf("p2.Name=%v p1.Name=%v\n", (*p2).Name, p1.Name)
//
//	fmt.Printf("p1的地址是：%p\n", &p1)
//	fmt.Printf("p2的地址是：%p p2的值是：%p\n", &p2, p2)
//
//}

//type Person struct {
//	Name string `json:"name" db:"name"`
//	Age  int    `json:"age" db:"age"`
//}
//
//func main() {
//	// 获取Person结构体的反射类型
//	t := reflect.TypeOf(Person{})
//
//	// 遍历结构体的所有字段
//	for i := range t.NumField() {
//		// 输出字段名称及对应的JSON和DB标签
//		fmt.Printf("字段名称:%s，JSON tag:%s，DB tag:%s\n",
//			t.Field(i).Name,
//			t.Field(i).Tag.Get("json"),
//			t.Field(i).Tag.Get("db"))
//	}
//}

//type Circle struct {
//	Radius float64
//}
//
//func (c Circle) Area() float64 {
//	return c.Radius * c.Radius * 3.14
//}
//
//func main() {
//	c := Circle{5.0}
//	fmt.Println(c.Area())
//}

//type MethodUtils struct {
//}
//
//func (mu MethodUtils)print(num1 int , num2 int)  {
//	for i := 0; i < num1; i++ {
//		for j := 0; j < num2; j++ {
//			fmt.Print("*")
//		}
//		fmt.Println()
//	}
//
//}
//
//func main() {
//	mu := MethodUtils{}
//	mu.print(5, 5)
//}

type Student struct {
	Name   string
	Gender string
	Age    string
	id     string
	score  string
}

func (student Student) say() string {
	return "我是：" + student.Name + "，性别：" + student.Gender + "，年龄：" + student.Age
}

func main() {
	student := Student{"张三", "男", "18", "1001", "90"}
	fmt.Println(student.say())
}
