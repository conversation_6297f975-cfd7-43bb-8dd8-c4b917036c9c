package main

import "fmt"

//
//type Usb interface {
//	Start()
//	Stop()
//}
//type Phone struct {
//}
//
//type Camera struct {
//}
//
//func (phone Phone) Start() {
//	fmt.Println("手机开始工作")
//}
//
//func (phone Phone) Stop() {
//	fmt.Println("手机停止工作")
//}
//
//func (camera Camera) Start() {
//	fmt.Println("相机开始工作")
//}
//
//func (camera Camera) Stop() {
//	fmt.Println("相机停止工作")
//}
//
//type Computer struct {
//}
//
//func (computer Computer) Working(usb Usb) {
//	usb.Start()
//	usb.Stop()
//}
//
//func main() {
//	// 创建计算机实例
//	computer := Computer{}
//	// 定义USB接口变量
//	var usb Usb
//
//	// 使用手机作为USB设备
//	usb = Phone{}
//	computer.Working(usb) // 调用计算机的Working方法
//
//	// 使用相机作为USB设备
//	usb = Camera{}
//	computer.Working(usb) // 调用计算机的Working方法
//}

//	type Hero struct {
//		Name string
//		Age  int
//	}
//
// type HeroSlice []Hero
//
//	func (h HeroSlice) Len() int {
//		return len(h)
//	}
//
//	func (h HeroSlice) Less(i, j int) bool {
//		return h[i].Age < h[j].Age
//	}
//
//	func (h HeroSlice) Swap(i, j int) {
//		h[i], h[j] = h[j], h[i]
//	}
//
//	func main() {
//		heroes := HeroSlice{
//			{Name: "小明", Age: 10},
//			{Name: "小王", Age: 9},
//			{Name: "小张", Age: 13},
//		}
//		fmt.Printf("排序前：%v\n", heroes)
//		sort.Sort(heroes)
//		fmt.Printf("排序后：%v\n", heroes)
//	}
type Monkey struct {
	name string
}

func (m Monkey) climb() {
	fmt.Println(m.name, "会爬树")
}

type LittleMonkey struct {
	Monkey
}

type BirdAble interface {
	Fly()
}

func (l LittleMonkey) Fly() {
	fmt.Println(l.name, "会飞翔")
}

func main() {
	littleMonkey := LittleMonkey{Monkey{"wukong"}}
	littleMonkey.climb()
	littleMonkey.Fly()
}
